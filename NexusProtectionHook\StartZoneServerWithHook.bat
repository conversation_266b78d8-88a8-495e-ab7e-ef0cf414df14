@echo off
title Nexus Protection - Zone Server with Hook
echo.
echo ========================================
echo   Nexus Protection Zone Server Launcher
echo ========================================
echo.

REM Check if ZoneServerUD_x64.exe exists
if not exist "ZoneServerUD_x64.exe" (
    echo ERROR: ZoneServerUD_x64.exe not found in current directory!
    echo.
    echo Please copy this batch file to the same folder as ZoneServerUD_x64.exe
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

echo Found ZoneServerUD_x64.exe
echo.

REM Check if DLL injector exists
set INJECTOR_PATH=""
if exist "DLLInjector.exe" (
    set INJECTOR_PATH="DLLInjector.exe"
) else if exist "bin\DLLInjector.exe" (
    set INJECTOR_PATH="bin\DLLInjector.exe"
) else if exist "bin\Release\DLLInjector.exe" (
    set INJECTOR_PATH="bin\Release\DLLInjector.exe"
) else if exist "bin\Debug\DLLInjector.exe" (
    set INJECTOR_PATH="bin\Debug\DLLInjector.exe"
) else (
    echo WARNING: DLL Injector not found!
    echo Zone server will start without hook.
    echo.
    echo To use the hook, build the project first and copy DLLInjector.exe here.
    echo.
    set INJECTOR_PATH=""
)

if not %INJECTOR_PATH%=="" (
    echo Found DLL Injector: %INJECTOR_PATH%
    echo.
)

echo Starting Zone Server...
echo.

REM Start the zone server
start "Zone Server" ZoneServerUD_x64.exe

REM Wait a bit for the server to start
echo Waiting for zone server to start...
timeout /t 3 /nobreak >nul

REM If we have an injector, run it
if not %INJECTOR_PATH%=="" (
    echo.
    echo Injecting Nexus Protection Hook...
    echo.
    %INJECTOR_PATH%
) else (
    echo.
    echo Zone server started without hook.
    echo To add the hook later, run DLLInjector.exe while the server is running.
)

echo.
echo Done! Check the zone server window for "Nexus Protection" text.
echo.
pause
