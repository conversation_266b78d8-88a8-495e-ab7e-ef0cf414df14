@echo off
echo === Quick Build Enhanced Hook ===
echo.

REM Try to find Visual Studio
set "VSPATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
) else (
    echo Visual Studio not found!
    echo Please run this from a Visual Studio Developer Command Prompt.
    pause
    exit /b 1
)

echo Found Visual Studio at: %VSPATH%
echo Setting up environment...
call "%VSPATH%"

echo.
echo Creating directories...
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"

echo.
echo Building Enhanced Hook DLL...
cl /LD /EHsc /O2 /std:c++17 /DDEBUG ^
   src\NexusProtectionHook.cpp ^
   /Fe:bin\Debug\NexusProtectionHook.dll ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build Hook DLL!
    pause
    exit /b 1
)

echo.
echo Building Enhanced Injector...
cl /EHsc /O2 /std:c++17 /utf-8 ^
   src\DLLInjector.cpp ^
   /Fe:bin\Debug\DLLInjector.exe ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build Injector!
    pause
    exit /b 1
)

echo.
echo === BUILD SUCCESSFUL! ===
echo.
echo Enhanced features added:
echo - Immediate dialog modification on injection
echo - Periodic scanning for new dialogs (every 500ms)
echo - Better text pattern matching
echo - Debug output (when compiled with DEBUG)
echo.
echo Files created:
echo - bin\Debug\NexusProtectionHook.dll (Enhanced Hook)
echo - bin\Debug\DLLInjector.exe (Enhanced Injector)
echo.
echo === TESTING INSTRUCTIONS ===
echo 1. Close your zone server completely
echo 2. Run: bin\Debug\DLLInjector.exe (should say "process not found")
echo 3. Start your zone server
echo 4. QUICKLY run: bin\Debug\DLLInjector.exe (while loading dialog is visible)
echo 5. The dialog should change to "Nexus Protection"!
echo.
pause
