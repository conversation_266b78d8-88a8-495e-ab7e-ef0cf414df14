@echo off
echo Creating necessary directories...

if not exist "bin" mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"
if not exist "bin\Release" mkdir "bin\Release"
if not exist "build" mkdir build

echo Cleaning any locked files...
del /f /q "bin\Debug\*.exe" 2>nul
del /f /q "bin\Debug\*.dll" 2>nul
del /f /q "bin\Release\*.exe" 2>nul
del /f /q "bin\Release\*.dll" 2>nul

echo Setting permissions...
icacls "bin" /grant %USERNAME%:F /T 2>nul
icacls "build" /grant %USERNAME%:F /T 2>nul

echo Done! Try building in Visual Studio now.
pause
