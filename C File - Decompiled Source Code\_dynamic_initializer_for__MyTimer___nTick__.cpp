// Decompiled Function: _dynamic_initializer_for__MyTimer::_nTick__
// Address: 0x1406E5B50

DWORD dynamic_initializer_for__MyTimer::_nTick__()
{
  __int64 *v0; // rdi
  __int64 i; // rcx
  DWORD result; // eax
  __int64 v3; // [rsp+0h] [rbp-28h] BYREF

  v0 = &v3;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  result = timeGetTime();
  MyTimer::_nTick = result;
  return result;
}

