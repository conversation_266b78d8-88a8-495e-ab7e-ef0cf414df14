@echo off
echo === Building Enhanced DLL Injector ===
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio compiler not found in PATH.
    echo Please run this from a Visual Studio Developer Command Prompt.
    echo.
    echo Or run: "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    pause
    exit /b 1
)

echo Visual Studio compiler found!
echo.

REM Create directories
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"

echo Building Enhanced DLL Injector...
echo.

REM Build DLL Injector with Korean character support
cl /EHsc /O2 /std:c++17 /utf-8 ^
   src\DLLInjector.cpp ^
   /Fe:bin\Debug\DLLInjector.exe ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build DLL Injector!
    pause
    exit /b 1
)

echo.
echo Building Hook DLL...
echo.

REM Build Hook DLL
cl /LD /EHsc /O2 /std:c++17 ^
   src\NexusProtectionHook.cpp ^
   /Fe:bin\Debug\NexusProtectionHook.dll ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build Hook DLL!
    pause
    exit /b 1
)

echo.
echo === BUILD SUCCESSFUL! ===
echo.
echo Enhanced features:
echo - Smart process detection
echo - Support for Korean process names
echo - Multiple candidate selection
echo - Better error messages
echo.
echo Files created:
echo - bin\Debug\DLLInjector.exe (Enhanced injector)
echo - bin\Debug\NexusProtectionHook.dll (Hook DLL)
echo.
echo Now you can:
echo 1. Start your zone server (ZoneServerUD_x64.exe)
echo 2. Run: bin\Debug\DLLInjector.exe
echo 3. The injector will automatically find your zone server!
echo.
pause
