// Decompiled Function: ?PerlinNoise_2D@@YAMMMMK@Z
// Address: 0x140523890

float __fastcall PerlinNoise_2D(float a1, float a2, float a3, int a4)
{
  float v4; // xmm7_4
  unsigned int v5; // edi
  int v6; // ebx
  float v8; // xmm6_4
  float v9; // xmm0_4

  v4 = 0.0;
  v5 = a4 - 1;
  v6 = 0;
  if ( a4 != 1 )
  {
    do
    {
      v8 = (float)v6;
      v9 = InterpolatedNoise_1((float)((float)v6 * 2.0) * a1, (float)((float)v6 * 2.0) * a2);
      ++v6;
      v4 = v4 + (float)(v9 * (float)(v8 * a3));
    }
    while ( v6 < v5 );
  }
  return v4;
}

