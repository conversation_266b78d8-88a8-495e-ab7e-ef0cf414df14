// Decompiled Function: ??$_Move_cat@PEAVCUnmannedTraderGroupDivisionVersionInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderGroupDivisionVersionInfo@@@Z
// Address: 0x14039B240

std::_Undefined_move_tag __fastcall std::_Move_cat<CUnmannedTraderGroupDivisionVersionInfo *>(
        CUnmannedTraderGroupDivisionVersionInfo *const *__formal)
{
  __int64 *v1; // rdi
  __int64 i; // rcx
  __int64 v4; // [rsp+0h] [rbp-48h] BYREF
  std::_Undefined_move_tag _Cat; // [rsp+24h] [rbp-24h]

  v1 = &v4;
  for ( i = 16LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return _Cat;
}

