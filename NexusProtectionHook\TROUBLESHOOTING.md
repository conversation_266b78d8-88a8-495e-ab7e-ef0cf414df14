# Troubleshooting Guide

## 🔍 Problem: "Zone Server process not found!"

This error means the injector can't find a running zone server process. Let's fix this step by step.

### Step 1: Check if Zone Server is Actually Running

1. **Build the ProcessChecker tool:**
   - Open `NexusProtectionHook.sln` in Visual Studio 2022
   - Build Solution (Ctrl+Shift+B)
   - Make sure `ProcessChecker` project builds successfully

2. **Run the ProcessChecker:**
   ```batch
   cd bin\Debug
   ProcessChecker.exe
   ```

3. **What to look for:**
   - If zone server is running, you'll see it in the list
   - If not running, you'll see all processes and can identify the correct name

### Step 2: Start Zone Server Correctly

The zone server must be **running as a process**, not just the file existing.

**Method A: Double-click the executable**
1. Navigate to your zone server folder
2. Double-click `ZoneServerUD_x64.exe`
3. Wait for it to start (you should see a window/dialog)
4. Keep it running - don't close it

**Method B: Command line**
```batch
cd "C:\Path\To\Your\ZoneServer"
ZoneServerUD_x64.exe
```

### Step 3: Verify Process is Running

1. **Open Task Manager** (Ctrl+Shift+Esc)
2. **Go to Details tab**
3. **Look for:** `ZoneServerUD_x64.exe`
4. **If you see it:** Great! The process is running
5. **If you don't see it:** The zone server isn't actually running

### Step 4: Common Issues

**Issue: Zone server starts then immediately closes**
- Check if all required files are present
- Look for error messages in console
- Try running as Administrator

**Issue: Zone server won't start**
- Check if ports are already in use
- Verify all dependencies are installed
- Check antivirus isn't blocking it

**Issue: Different process name**
- Your zone server might have a different name
- Use ProcessChecker to see all running processes
- Look for anything with "zone", "server", or "rf" in the name

### Step 5: Alternative Process Names

Your zone server might be named differently. Try these:
- `ZoneServer.exe`
- `ZoneServerUD.exe`
- `zoneserver.exe`
- `RFServer.exe`
- `RFZoneServer.exe`

### Step 6: Manual Process Check

If ProcessChecker doesn't work, manually check:

1. **Open Command Prompt**
2. **Run:** `tasklist | findstr /i zone`
3. **Or:** `tasklist | findstr /i server`
4. **Look for** your zone server process

### Step 7: Test with Simple Process

To verify the injector works, test with a simple process:

1. **Open Notepad** (notepad.exe)
2. **Modify the injector** to look for "notepad.exe"
3. **Run the injector** - it should find notepad
4. **If it finds notepad** but not zone server, the issue is with zone server startup

## 🎯 Quick Checklist

Before running the injector:

- [ ] Zone server executable exists
- [ ] Zone server is actually running (visible in Task Manager)
- [ ] Zone server window/dialog is open
- [ ] No antivirus blocking the zone server
- [ ] Running with sufficient permissions
- [ ] Correct process name in injector code

## 🔧 Debug Steps

1. **Build and run ProcessChecker.exe**
2. **Start zone server**
3. **Run ProcessChecker.exe again** - zone server should appear
4. **Note the exact process name**
5. **Update injector if needed** with correct process name
6. **Try injection again**

## 📞 Still Not Working?

If zone server still won't start or be detected:

1. **Check zone server logs** for error messages
2. **Try running zone server as Administrator**
3. **Disable antivirus temporarily**
4. **Use the Auto-Loader method instead** (version.dll hijacking)
5. **Verify zone server is compatible** with your Windows version

## 🎮 Alternative: Use Auto-Loader

If manual injection keeps failing, use the auto-loader method:

1. **Build the AutoLoader project**
2. **Run SetupAutoLoader.bat**
3. **Copy version.dll to zone server folder**
4. **Start zone server normally** - hook loads automatically

This bypasses the need for manual process injection entirely.
