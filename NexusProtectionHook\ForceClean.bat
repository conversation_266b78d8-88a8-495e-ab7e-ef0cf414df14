@echo off
echo === Force Clean and Fix Build Issues ===
echo.

echo Killing any running processes...
taskkill /f /im DLLInjector.exe 2>nul
taskkill /f /im NexusProtectionHook.dll 2>nul
taskkill /f /im ProcessChecker.exe 2>nul

echo.
echo Waiting 2 seconds...
timeout /t 2 /nobreak >nul

echo.
echo Removing locked files...
del /f /q "bin\Debug\DLLInjector.exe" 2>nul
del /f /q "bin\Release\DLLInjector.exe" 2>nul
del /f /q "bin\Debug\*.dll" 2>nul
del /f /q "bin\Release\*.dll" 2>nul
del /f /q "bin\Debug\*.pdb" 2>nul
del /f /q "bin\Release\*.pdb" 2>nul

echo.
echo Cleaning build directories...
rmdir /s /q "build" 2>nul
rmdir /s /q "bin" 2>nul

echo.
echo Recreating directories...
mkdir bin
mkdir "bin\Debug"
mkdir "bin\Release"
mkdir build

echo.
echo Setting full permissions...
icacls bin /grant %USERNAME%:F /T >nul 2>&1
icacls build /grant %USERNAME%:F /T >nul 2>&1

echo.
echo === Clean Complete! ===
echo.
echo Now try:
echo 1. Close Visual Studio completely
echo 2. Run Visual Studio as Administrator
echo 3. Open the solution
echo 4. Build Solution
echo.
pause
