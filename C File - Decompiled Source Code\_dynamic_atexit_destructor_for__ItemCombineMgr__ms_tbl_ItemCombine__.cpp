// Decompiled Function: _dynamic_atexit_destructor_for__ItemCombineMgr::ms_tbl_ItemCombine__
// Address: 0x1406E8F60

void __fastcall dynamic_atexit_destructor_for__ItemCombineMgr::ms_tbl_ItemCombine__()
{
  __int64 *v0; // rdi
  __int64 i; // rcx
  __int64 v2; // [rsp+0h] [rbp-28h] BYREF

  v0 = &v2;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector destructor iterator'(
    (char *)ItemCombineMgr::ms_tbl_ItemCombine,
    0xB0uLL,
    1,
    (void (__fastcall *)(void *))CRecordData::~CRecordData);
}

