# CMakeLists.txt for Nexus Protection Hook DLL

cmake_minimum_required(VERSION 3.16)
project(NexusProtectionHook)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Find Detours library (you need to install Microsoft Detours)
# Download from: https://github.com/microsoft/Detours
find_path(DETOURS_INCLUDE_DIR detours.h PATHS
    "C:/Program Files/Microsoft Research/Detours/include"
    "C:/Detours/include"
    "${CMAKE_SOURCE_DIR}/detours/include"
)

find_library(DETOURS_LIBRARY detours PATHS
    "C:/Program Files/Microsoft Research/Detours/lib.X64"
    "C:/Detours/lib.X64"
    "${CMAKE_SOURCE_DIR}/detours/lib.X64"
)

if(NOT DETOURS_INCLUDE_DIR OR NOT DETOURS_LIBRARY)
    message(FATAL_ERROR "Microsoft Detours not found. Please install Detours and set the paths correctly.")
endif()

# Hook DLL
add_library(NexusProtectionHook SHARED
    src/NexusProtectionHook.cpp
    src/NexusProtectionHook.h
)

target_include_directories(NexusProtectionHook PRIVATE ${DETOURS_INCLUDE_DIR})
target_link_libraries(NexusProtectionHook ${DETOURS_LIBRARY})

# DLL Injector executable
add_executable(DLLInjector
    src/DLLInjector.cpp
)

# Set compiler flags for Windows
if(WIN32)
    target_compile_definitions(NexusProtectionHook PRIVATE WIN32_LEAN_AND_MEAN)
    target_compile_definitions(DLLInjector PRIVATE WIN32_LEAN_AND_MEAN)
endif()

# Copy DLL to output directory after build
add_custom_command(TARGET NexusProtectionHook POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    $<TARGET_FILE:NexusProtectionHook>
    ${CMAKE_BINARY_DIR}/bin/
)
