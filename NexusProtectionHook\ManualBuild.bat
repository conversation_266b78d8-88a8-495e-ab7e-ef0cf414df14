@echo off
echo === Manual Build Script ===
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio compiler not found in PATH.
    echo Please run this from a Visual Studio Developer Command Prompt.
    echo.
    echo Or run: "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    pause
    exit /b 1
)

echo Visual Studio compiler found!
echo.

REM Create directories
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"
if not exist "bin\Release" mkdir "bin\Release"

echo Building DLL Injector...
echo.

REM Build DLL Injector
cl /EHsc /O2 /std:c++17 ^
   src\DLLInjector.cpp ^
   /Fe:bin\Debug\DLLInjector.exe ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build DLL Injector!
    pause
    exit /b 1
)

echo.
echo Building Hook DLL...
echo.

REM Build Hook DLL
cl /LD /EHsc /O2 /std:c++17 ^
   src\NexusProtectionHook.cpp ^
   /Fe:bin\Debug\NexusProtectionHook.dll ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build Hook DLL!
    pause
    exit /b 1
)

echo.
echo Building Process Checker...
echo.

REM Build Process Checker
cl /EHsc /O2 /std:c++17 ^
   src\ProcessChecker.cpp ^
   /Fe:bin\Debug\ProcessChecker.exe ^
   /Fo:bin\Debug\ ^
   kernel32.lib user32.lib

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo WARNING: Failed to build Process Checker (not critical)
)

echo.
echo === BUILD SUCCESSFUL! ===
echo.
echo Files created in bin\Debug\:
dir bin\Debug\*.exe bin\Debug\*.dll 2>nul
echo.
echo You can now run:
echo - bin\Debug\DLLInjector.exe (to inject the hook)
echo - bin\Debug\ProcessChecker.exe (to check running processes)
echo.
pause
