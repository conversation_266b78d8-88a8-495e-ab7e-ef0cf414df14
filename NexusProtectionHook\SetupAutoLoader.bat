@echo off
title Nexus Protection Auto-Loader Setup
echo.
echo ==========================================
echo   Nexus Protection Auto-Loader Setup
echo ==========================================
echo.

REM Check if the auto-loader DLL exists
set DLL_PATH=""
if exist "bin\Release\version.dll" (
    set DLL_PATH="bin\Release\version.dll"
    echo Found Auto-Loader DLL: bin\Release\version.dll
) else if exist "bin\Debug\version.dll" (
    set DLL_PATH="bin\Debug\version.dll"
    echo Found Auto-Loader DLL: bin\Debug\version.dll
) else (
    echo ERROR: Auto-Loader DLL not found!
    echo.
    echo Please build the project first:
    echo 1. Open NexusProtectionHook.sln in Visual Studio 2022
    echo 2. Set platform to x64
    echo 3. Build Solution (Ctrl+Shift+B)
    echo 4. Make sure "AutoLoader" project builds successfully
    echo.
    pause
    exit /b 1
)

echo.

REM Ask user for zone server directory
set /p ZONE_DIR="Enter the full path to your Zone Server directory: "

REM Remove quotes if present
set ZONE_DIR=%ZONE_DIR:"=%

REM Check if directory exists
if not exist "%ZONE_DIR%" (
    echo.
    echo ERROR: Directory does not exist: %ZONE_DIR%
    echo.
    pause
    exit /b 1
)

REM Check if ZoneServerUD_x64.exe exists in that directory
if not exist "%ZONE_DIR%\ZoneServerUD_x64.exe" (
    echo.
    echo ERROR: ZoneServerUD_x64.exe not found in: %ZONE_DIR%
    echo.
    echo Please make sure you entered the correct zone server directory.
    echo.
    pause
    exit /b 1
)

echo.
echo Found ZoneServerUD_x64.exe in: %ZONE_DIR%
echo.

REM Copy the DLL to the zone server directory
echo Copying Auto-Loader DLL to zone server directory...
copy %DLL_PATH% "%ZONE_DIR%\version.dll" >nul

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to copy DLL to zone server directory!
    echo.
    echo Possible causes:
    echo - Insufficient permissions (try running as Administrator)
    echo - Zone server is currently running (close it first)
    echo - Antivirus blocking the operation
    echo.
    pause
    exit /b 1
)

echo.
echo ==========================================
echo   SUCCESS! Auto-Loader Setup Complete
echo ==========================================
echo.
echo The Nexus Protection Hook has been installed!
echo.
echo What happens now:
echo 1. When you start ZoneServerUD_x64.exe, it will automatically load version.dll
echo 2. The hook will replace "RF Server Now Loading.." with "Nexus Protection"
echo 3. No manual injection needed - it works automatically!
echo.
echo Files installed:
echo - %ZONE_DIR%\version.dll (Nexus Protection Hook)
echo.
echo To uninstall:
echo - Simply delete version.dll from the zone server directory
echo.
echo You can now start your zone server normally and see "Nexus Protection"!
echo.
pause
