// Decompiled Function: ?gm_MainThreadControl@CMainThread@@QEAAXXZ
// Address: 0x1401F75F0

void __fastcall CMainThread::gm_MainThreadControl(CMainThread *this)
{
  __int64 *v1; // rdi
  __int64 i; // rcx
  __int64 v3; // [rsp+0h] [rbp-88h] BYREF
  CHAR szCheckSum[56]; // [rsp+38h] [rbp-50h] BYREF

  v1 = &v3;
  for ( i = 32LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  this->m_nSleepTerm = GetPrivateProfileIntA("MainThread", "SleepTerm", 2, ".\\Initialize\\WorldSystem.ini");
  this->m_nSleepValue = GetPrivateProfileIntA("MainThread", "SleepValue", 1, ".\\Initialize\\WorldSystem.ini");
  this->m_nSleepIgnore = GetPrivateProfileIntA("MainThread", "SleepIgnore", 0, ".\\Initialize\\WorldSystem.ini");
  if ( this->m_nSleepTerm < 0 )
    this->m_nSleepTerm = 0;
  if ( this->m_nSleepValue < 0 )
    this->m_nSleepValue = 0;
  if ( this->m_nSleepValue > 10 )
    this->m_nSleepValue = 10;
  if ( this->m_nSleepIgnore >= 2u )
    this->m_nSleepIgnore = 0;
  _ATTACK_DELAY_CHECKER::s_nSpareTime = GetPrivateProfileIntA(
                                          "Rule",
                                          "AttackSpareDelay",
                                          100,
                                          ".\\Initialize\\WorldSystem.ini");
  this->m_nLimUserNum = GetPrivateProfileIntA("System", "LimUserNum", 2532, ".\\Initialize\\WorldSystem.ini");
  if ( this->m_nLimUserNum > 2532 )
    this->m_nLimUserNum = 2532;
  memset(szCheckSum, 0, 32);
  GetPrivateProfileStringA("System", "CheckSum", "TRUE", szCheckSum, 0x20u, ".\\Initialize\\WorldSystem.ini");
  this->m_bCheckSumActive = !strcmp_0(szCheckSum, "TRUE");
  CWnd::SendMessageA(g_pFrame, 0xCu, 0LL, 0LL);
}

