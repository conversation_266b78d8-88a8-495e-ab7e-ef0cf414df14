// DLLInjector.cpp - Utility to inject NexusProtectionHook.dll into ZoneServer

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>

class DLLInjector
{
public:
    static DWORD GetProcessID(const std::wstring& processName)
    {
        DWORD processID = 0;
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        
        if (hSnapshot != INVALID_HANDLE_VALUE)
        {
            PROCESSENTRY32W pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32W);
            
            if (Process32FirstW(hSnapshot, &pe32))
            {
                do
                {
                    if (processName == pe32.szExeFile)
                    {
                        processID = pe32.th32ProcessID;
                        break;
                    }
                } while (Process32NextW(hSnapshot, &pe32));
            }
            CloseHandle(hSnapshot);
        }
        return processID;
    }
    
    static bool InjectDLL(DWORD processID, const std::string& dllPath)
    {
        // Open target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processID);
        if (!hProcess)
        {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        // Allocate memory in target process
        SIZE_T pathSize = dllPath.length() + 1;
        LPVOID pRemotePath = VirtualAllocEx(hProcess, NULL, pathSize, MEM_COMMIT, PAGE_READWRITE);
        if (!pRemotePath)
        {
            std::cout << "Failed to allocate memory in target process." << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // Write DLL path to target process memory
        if (!WriteProcessMemory(hProcess, pRemotePath, dllPath.c_str(), pathSize, NULL))
        {
            std::cout << "Failed to write DLL path to target process." << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        LPTHREAD_START_ROUTINE pLoadLibrary = (LPTHREAD_START_ROUTINE)GetProcAddress(hKernel32, "LoadLibraryA");
        
        // Create remote thread to load our DLL
        HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, pLoadLibrary, pRemotePath, 0, NULL);
        if (!hThread)
        {
            std::cout << "Failed to create remote thread." << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Wait for injection to complete
        WaitForSingleObject(hThread, INFINITE);
        
        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        
        return true;
    }
};

int main(int argc, char* argv[])
{
    std::cout << "=== Nexus Protection Hook Injector ===" << std::endl;
    
    // Get current directory for DLL path
    char currentDir[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, currentDir);
    std::string dllPath = std::string(currentDir) + "\\NexusProtectionHook.dll";
    
    std::cout << "Looking for ZoneServerUD_x64.exe process..." << std::endl;
    
    // Find ZoneServer process
    DWORD processID = DLLInjector::GetProcessID(L"ZoneServerUD_x64.exe");
    if (processID == 0)
    {
        std::cout << "ZoneServerUD_x64.exe not found. Please start the zone server first." << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    std::cout << "Found ZoneServerUD_x64.exe (PID: " << processID << ")" << std::endl;
    std::cout << "Injecting DLL: " << dllPath << std::endl;
    
    // Inject our hook DLL
    if (DLLInjector::InjectDLL(processID, dllPath))
    {
        std::cout << "SUCCESS! Nexus Protection Hook injected successfully!" << std::endl;
        std::cout << "The loading dialog should now show 'Nexus Protection' instead of 'RF Server Now Loading..'" << std::endl;
    }
    else
    {
        std::cout << "FAILED! Could not inject DLL into ZoneServer process." << std::endl;
    }
    
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    return 0;
}
