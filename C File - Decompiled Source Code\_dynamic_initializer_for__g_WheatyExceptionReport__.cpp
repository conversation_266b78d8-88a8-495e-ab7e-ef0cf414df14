// Decompiled Function: _dynamic_initializer_for__g_WheatyExceptionReport__
// Address: 0x1406E5E60

__int64 dynamic_initializer_for__g_WheatyExceptionReport__()
{
  __int64 *v0; // rdi
  __int64 i; // rcx
  __int64 v3; // [rsp+0h] [rbp-28h] BYREF

  v0 = &v3;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  WheatyExceptionReport::WheatyExceptionReport(&g_WheatyExceptionReport);
  return atexit((int (__fastcall *)())dynamic_atexit_destructor_for__g_WheatyExceptionReport__);
}

