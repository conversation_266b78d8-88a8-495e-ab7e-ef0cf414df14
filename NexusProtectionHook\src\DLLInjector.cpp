// DLLInjector.cpp - Utility to inject NexusProtectionHook.dll into ZoneServer

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>
#include <vector>

class DLLInjector
{
public:
    static DWORD GetProcessID(const std::wstring& processName)
    {
        DWORD processID = 0;
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        
        if (hSnapshot != INVALID_HANDLE_VALUE)
        {
            PROCESSENTRY32W pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32W);
            
            if (Process32FirstW(hSnapshot, &pe32))
            {
                do
                {
                    if (processName == pe32.szExeFile)
                    {
                        processID = pe32.th32ProcessID;
                        break;
                    }
                } while (Process32NextW(hSnapshot, &pe32));
            }
            CloseHandle(hSnapshot);
        }
        return processID;
    }
    
    static bool InjectDLL(DWORD processID, const std::string& dllPath)
    {
        // Open target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processID);
        if (!hProcess)
        {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        // Allocate memory in target process
        SIZE_T pathSize = dllPath.length() + 1;
        LPVOID pRemotePath = VirtualAllocEx(hProcess, NULL, pathSize, MEM_COMMIT, PAGE_READWRITE);
        if (!pRemotePath)
        {
            std::cout << "Failed to allocate memory in target process." << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // Write DLL path to target process memory
        if (!WriteProcessMemory(hProcess, pRemotePath, dllPath.c_str(), pathSize, NULL))
        {
            std::cout << "Failed to write DLL path to target process." << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        LPTHREAD_START_ROUTINE pLoadLibrary = (LPTHREAD_START_ROUTINE)GetProcAddress(hKernel32, "LoadLibraryA");
        
        // Create remote thread to load our DLL
        HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, pLoadLibrary, pRemotePath, 0, NULL);
        if (!hThread)
        {
            std::cout << "Failed to create remote thread." << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Wait for injection to complete
        WaitForSingleObject(hThread, INFINITE);
        
        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        
        return true;
    }
};

int main(int argc, char* argv[])
{
    std::cout << "=== Nexus Protection Hook Injector ===" << std::endl;
    std::cout << std::endl;

    // Get current directory for DLL path
    char currentDir[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, currentDir);

    // Try multiple possible DLL locations
    std::string dllPath1 = std::string(currentDir) + "\\NexusProtectionHook.dll";
    std::string dllPath2 = std::string(currentDir) + "\\bin\\NexusProtectionHook.dll";
    std::string dllPath3 = std::string(currentDir) + "\\bin\\Release\\NexusProtectionHook.dll";
    std::string dllPath4 = std::string(currentDir) + "\\bin\\Debug\\NexusProtectionHook.dll";

    std::string dllPath;
    if (GetFileAttributesA(dllPath1.c_str()) != INVALID_FILE_ATTRIBUTES) {
        dllPath = dllPath1;
    } else if (GetFileAttributesA(dllPath2.c_str()) != INVALID_FILE_ATTRIBUTES) {
        dllPath = dllPath2;
    } else if (GetFileAttributesA(dllPath3.c_str()) != INVALID_FILE_ATTRIBUTES) {
        dllPath = dllPath3;
    } else if (GetFileAttributesA(dllPath4.c_str()) != INVALID_FILE_ATTRIBUTES) {
        dllPath = dllPath4;
    } else {
        std::cout << "ERROR: NexusProtectionHook.dll not found!" << std::endl;
        std::cout << "Searched in:" << std::endl;
        std::cout << "  " << dllPath1 << std::endl;
        std::cout << "  " << dllPath2 << std::endl;
        std::cout << "  " << dllPath3 << std::endl;
        std::cout << "  " << dllPath4 << std::endl;
        std::cout << std::endl;
        std::cout << "Please make sure the DLL is built and in the correct location." << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    std::cout << "Found DLL: " << dllPath << std::endl;
    std::cout << std::endl;

    // Try multiple possible process names
    std::vector<std::wstring> processNames = {
        L"ZoneServerUD_x64.exe",
        L"ZoneServer.exe",
        L"ZoneServerUD.exe",
        L"zoneserver.exe"
    };

    DWORD processID = 0;
    std::wstring foundProcessName;

    std::cout << "Looking for Zone Server process..." << std::endl;
    for (const auto& processName : processNames) {
        std::wcout << L"  Checking for: " << processName << std::endl;
        processID = DLLInjector::GetProcessID(processName);
        if (processID != 0) {
            foundProcessName = processName;
            break;
        }
    }

    if (processID == 0)
    {
        std::cout << std::endl;
        std::cout << "ERROR: Zone Server process not found!" << std::endl;
        std::cout << "Please start the zone server first, then run this injector." << std::endl;
        std::cout << std::endl;
        std::cout << "Make sure one of these processes is running:" << std::endl;
        for (const auto& processName : processNames) {
            std::wcout << L"  - " << processName << std::endl;
        }
        std::cout << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    std::wcout << L"Found process: " << foundProcessName << L" (PID: " << processID << L")" << std::endl;
    std::cout << "Injecting DLL..." << std::endl;
    std::cout << std::endl;

    // Inject our hook DLL
    if (DLLInjector::InjectDLL(processID, dllPath))
    {
        std::cout << "SUCCESS! Nexus Protection Hook injected successfully!" << std::endl;
        std::cout << std::endl;
        std::cout << "The zone server should now show:" << std::endl;
        std::cout << "  - Loading dialog: 'Nexus Protection'" << std::endl;
        std::cout << "  - Window title: 'Nexus Protection: ServerName Server'" << std::endl;
        std::cout << std::endl;
        std::cout << "You can now restart the zone server to see the changes." << std::endl;
    }
    else
    {
        std::cout << "FAILED! Could not inject DLL into Zone Server process." << std::endl;
        std::cout << std::endl;
        std::cout << "Possible solutions:" << std::endl;
        std::cout << "  1. Run this injector as Administrator" << std::endl;
        std::cout << "  2. Disable antivirus temporarily" << std::endl;
        std::cout << "  3. Make sure zone server is running" << std::endl;
        std::cout << "  4. Check if DLL and EXE are same architecture (x64)" << std::endl;
    }

    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    return 0;
}
