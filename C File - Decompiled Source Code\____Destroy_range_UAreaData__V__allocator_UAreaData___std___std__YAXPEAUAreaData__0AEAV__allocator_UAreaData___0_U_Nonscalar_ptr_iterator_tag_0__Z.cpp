// Decompiled Function: ??$_Destroy_range@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@YAXPEAUAreaData@@0AEAV?$allocator@UAreaData@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
// Address: 0x140192E00

void __fastcall std::_Destroy_range<AreaData>(
        AreaData *_First,
        AreaData *_Last,
        std::allocator<AreaData> *_Al,
        std::_Nonscalar_ptr_iterator_tag __formal)
{
  __int64 *v4; // rdi
  __int64 i; // rcx
  __int64 v6; // [rsp+0h] [rbp-28h] BYREF

  v4 = &v6;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  while ( _First != _Last )
    std::allocator<AreaData>::destroy(_Al, _First++);
}

