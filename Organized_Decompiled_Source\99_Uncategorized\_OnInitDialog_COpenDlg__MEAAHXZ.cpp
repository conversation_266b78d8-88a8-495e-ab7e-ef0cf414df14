// Decompiled Function: ?OnInitDialog@COpenDlg@@MEAAHXZ
// Address: 0x140029270

__int64 __fastcall COpenDlg::OnInitDialog(COpenDlg *this)
{
  __int64 *v1; // rdi
  __int64 i; // rcx
  __int64 v4; // [rsp+0h] [rbp-28h] BYREF

  v1 = &v4;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CDialog::OnInitDialog(this);

  // Change the loading dialog text to "Nexus Protection"
  CWnd::SetWindowTextA(this, "Nexus Protection");

  return 1LL;
}

