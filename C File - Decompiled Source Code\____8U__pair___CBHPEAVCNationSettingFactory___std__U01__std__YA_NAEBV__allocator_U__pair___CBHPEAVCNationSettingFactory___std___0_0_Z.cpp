// Decompiled Function: ??$?8U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@U01@@std@@YA_NAEBV?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@0@0@Z
// Address: 0x140221330

char __fastcall std::operator==<std::pair<int const,CNationSettingFactory *>,std::pair<int const,CNationSettingFactory *>>(
        const std::allocator<std::pair<int const ,CNationSettingFactory *> > *__formal,
        const std::allocator<std::pair<int const ,CNationSettingFactory *> > *a2)
{
  return 1;
}

