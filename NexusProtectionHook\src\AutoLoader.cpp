// AutoLoader.cpp - Auto-loading version that doesn't require manual injection
// This DLL will be loaded automatically when the zone server starts

#include <windows.h>
#include <string>
#include <iostream>

// Original function pointer
static BOOL (WINAPI* OriginalSetWindowTextA)(HWND hWnd, LPCSTR lpString) = nullptr;

// Our hook function
BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString)
{
    // Check if this is the loading dialog text we want to replace
    if (lpString != nullptr)
    {
        std::string text(lpString);
        
        // Replace "RF Server Now Loading.." with "Nexus Protection"
        if (text.find("RF Server Now Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }
        
        // Also handle variations of the text
        if (text.find("RF Server") != std::string::npos && text.find("Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }
        
        // Handle window title changes for the main server window
        if (text.find("RF Online:") != std::string::npos)
        {
            // Replace "RF Online: ServerName Server" with "Nexus Protection: ServerName Server"
            size_t pos = text.find("RF Online:");
            if (pos != std::string::npos)
            {
                std::string newText = text;
                newText.replace(pos, 10, "Nexus Protection:");
                return OriginalSetWindowTextA(hWnd, newText.c_str());
            }
        }
    }
    
    // Call original function for all other text
    return OriginalSetWindowTextA(hWnd, lpString);
}

// Manual API hooking function
BOOL HookAPI(LPCSTR lpModuleName, LPCSTR lpProcName, LPVOID lpNewFunction, LPVOID* lpOriginalFunction)
{
    HMODULE hModule = GetModuleHandleA(lpModuleName);
    if (!hModule)
        return FALSE;

    LPVOID lpOriginalAddr = GetProcAddress(hModule, lpProcName);
    if (!lpOriginalAddr)
        return FALSE;

    *lpOriginalFunction = lpOriginalAddr;

    DWORD dwOldProtect;
    if (!VirtualProtect(lpOriginalAddr, 5, PAGE_EXECUTE_READWRITE, &dwOldProtect))
        return FALSE;

    // Create a jump to our function (JMP instruction)
    BYTE jmpInstruction[5];
    jmpInstruction[0] = 0xE9; // JMP opcode
    *(DWORD*)(jmpInstruction + 1) = (DWORD)((BYTE*)lpNewFunction - (BYTE*)lpOriginalAddr - 5);

    // Write the jump instruction
    memcpy(lpOriginalAddr, jmpInstruction, 5);

    VirtualProtect(lpOriginalAddr, 5, dwOldProtect, &dwOldProtect);
    return TRUE;
}

// Hook installation function
void InstallHooks()
{
    // Hook SetWindowTextA function
    if (HookAPI("user32.dll", "SetWindowTextA", (LPVOID)HookedSetWindowTextA, (LPVOID*)&OriginalSetWindowTextA))
    {
        // Optional: Create a log file to confirm loading
        HANDLE hFile = CreateFileA("NexusProtectionHook.log", GENERIC_WRITE, FILE_SHARE_READ, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile != INVALID_HANDLE_VALUE)
        {
            const char* message = "Nexus Protection Hook loaded successfully!\r\nSetWindowTextA hooked.\r\n";
            DWORD bytesWritten;
            WriteFile(hFile, message, strlen(message), &bytesWritten, NULL);
            CloseHandle(hFile);
        }
    }
}

// DLL Entry Point - This runs automatically when the DLL is loaded
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // Install hooks when DLL is loaded
        DisableThreadLibraryCalls(hModule);
        
        // Small delay to ensure the process is fully initialized
        Sleep(100);
        
        InstallHooks();
        break;
        
    case DLL_PROCESS_DETACH:
        // Cleanup when DLL is unloaded (optional)
        break;
    }
    return TRUE;
}

// Export functions (required for some DLL hijacking scenarios)
extern "C" __declspec(dllexport) void DummyExport()
{
    // This function exists just to make the DLL have exports
    // Some DLL hijacking methods require at least one export
}
