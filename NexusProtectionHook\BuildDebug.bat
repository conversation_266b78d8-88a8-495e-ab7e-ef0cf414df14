@echo off
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"
echo Building DEBUG version (with console output)...
cl /LD /EHsc /O2 /std:c++17 /MT /D_DEBUG src\NexusProtectionHook.cpp /Fe:bin\Debug\NexusProtectionHook.dll /Fo:bin\Debug\ kernel32.lib user32.lib
echo.
if exist "bin\Debug\NexusProtectionHook.dll" (
    echo SUCCESS: DEBUG DLL Built!
    echo.
    echo === TESTING INSTRUCTIONS ===
    echo 1. Start your zone server
    echo 2. Run: bin\Debug\DLLInjector.exe
    echo 3. Watch the console output for debug messages
    echo 4. The dialog should change to "Nexus Protection"
    echo.
    echo The debug version will show:
    echo - Which hooks were installed successfully
    echo - When dialog creation is intercepted
    echo - When text is changed
    echo.
    bin\Debug\DLLInjector.exe
) else (
    echo FAILED: DLL not created!
)
pause
