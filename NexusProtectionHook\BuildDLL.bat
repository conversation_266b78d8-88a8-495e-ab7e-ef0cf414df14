@echo off
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"
cl /LD /EHsc /O2 /std:c++17 /D_DEBUG src\NexusProtectionHook.cpp /Fe:bin\Debug\NexusProtectionHook.dll /Fo:bin\Debug\ kernel32.lib user32.lib
echo.
echo DLL Build Complete!
echo Testing with existing injector...
echo.
bin\Debug\DLLInjector.exe
pause
