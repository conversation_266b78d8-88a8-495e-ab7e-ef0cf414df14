// NexusProtectionHook.cpp - DLL to hook ZoneServer loading dialog
// Changes "RF Server Now Loading.." to "Nexus Protection"

#include <windows.h>
#include <detours.h>
#include <string>
#include <iostream>

#pragma comment(lib, "detours.lib")

// Original function pointer
static BOOL (WINAPI* TrueSetWindowTextA)(HWND hWnd, LPCSTR lpString) = SetWindowTextA;

// Our hook function
BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString)
{
    // Check if this is the loading dialog text we want to replace
    if (lpString != nullptr)
    {
        std::string text(lpString);
        
        // Replace "RF Server Now Loading.." with "Nexus Protection"
        if (text.find("RF Server Now Loading") != std::string::npos)
        {
            return TrueSetWindowTextA(hWnd, "Nexus Protection");
        }
        
        // Also handle variations of the text
        if (text.find("RF Server") != std::string::npos && text.find("Loading") != std::string::npos)
        {
            return TrueSetWindowTextA(hWnd, "Nexus Protection");
        }
        
        // Handle window title changes for the main server window
        if (text.find("RF Online:") != std::string::npos)
        {
            // Replace "RF Online: ServerName Server" with "Nexus Protection: ServerName Server"
            size_t pos = text.find("RF Online:");
            if (pos != std::string::npos)
            {
                std::string newText = text;
                newText.replace(pos, 10, "Nexus Protection:");
                return TrueSetWindowTextA(hWnd, newText.c_str());
            }
        }
    }
    
    // Call original function for all other text
    return TrueSetWindowTextA(hWnd, lpString);
}

// Hook installation function
void InstallHooks()
{
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    DetourAttach(&(PVOID&)TrueSetWindowTextA, HookedSetWindowTextA);
    DetourTransactionCommit();
}

// Hook removal function
void RemoveHooks()
{
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    DetourDetach(&(PVOID&)TrueSetWindowTextA, HookedSetWindowTextA);
    DetourTransactionCommit();
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // Install hooks when DLL is loaded
        DisableThreadLibraryCalls(hModule);
        InstallHooks();
        
        // Optional: Create a console for debugging
        #ifdef _DEBUG
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        std::cout << "[Nexus Protection Hook] DLL loaded and hooks installed!" << std::endl;
        #endif
        break;
        
    case DLL_PROCESS_DETACH:
        // Remove hooks when DLL is unloaded
        RemoveHooks();
        
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] DLL unloaded and hooks removed!" << std::endl;
        FreeConsole();
        #endif
        break;
    }
    return TRUE;
}

// Export function for manual injection (optional)
extern "C" __declspec(dllexport) void InitializeNexusHook()
{
    InstallHooks();
}

extern "C" __declspec(dllexport) void CleanupNexusHook()
{
    RemoveHooks();
}
