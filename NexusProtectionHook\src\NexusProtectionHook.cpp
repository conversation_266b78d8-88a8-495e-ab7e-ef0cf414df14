// NexusProtectionHook.cpp - DLL to hook ZoneServer loading dialog
// Changes "RF Server Now Loading.." to "Nexus Protection"
// Direct function patching approach using exact addresses from decompiled source

#include <windows.h>
#include <string>
#include <iostream>

// Target addresses from decompiled source
#define COPENDIG_ONINITDIALOG_ADDR 0x140029270  // COpenDlg::OnInitDialog address

// Original function pointers
static BOOL (WINAPI* OriginalSetWindowTextA)(HWND hWnd, LPCSTR lpString) = nullptr;
static BOOL (WINAPI* OriginalSetWindowTextW)(HWND hWnd, LPCWSTR lpString) = nullptr;
static HWND (WINAPI* OriginalCreateWindowExA)(DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName, DWORD dwStyle, int X, int Y, int nWidth, int nHeight, HW<PERSON> hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam) = nullptr;
static HWND (WINAPI* OriginalCreateWindowExW)(DWORD dwExStyle, LPCWSTR lpClassName, LPCWSTR lpWindowName, DWORD dwStyle, int X, int Y, int nWidth, int nHeight, HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam) = nullptr;
static HWND (WINAPI* OriginalCreateDialogParamA)(HINSTANCE hInstance, LPCSTR lpTemplateName, HWND hWndParent, DLGPROC lpDialogFunc, LPARAM dwInitParam) = nullptr;
static HWND (WINAPI* OriginalCreateDialogParamW)(HINSTANCE hInstance, LPCWSTR lpTemplateName, HWND hWndParent, DLGPROC lpDialogFunc, LPARAM dwInitParam) = nullptr;

// Our hook function
BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString)
{
    // Check if this is the loading dialog text we want to replace
    if (lpString != nullptr)
    {
        std::string text(lpString);

        // Replace "RF Server Now Loading.." with "Nexus Protection"
        if (text.find("RF Server Now Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }

        // Also handle variations of the text
        if (text.find("RF Server") != std::string::npos && text.find("Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }

        // Handle window title changes for the main server window
        if (text.find("RF Online:") != std::string::npos)
        {
            // Replace "RF Online: ServerName Server" with "Nexus Protection: ServerName Server"
            size_t pos = text.find("RF Online:");
            if (pos != std::string::npos)
            {
                std::string newText = text;
                newText.replace(pos, 10, "Nexus Protection:");
                return OriginalSetWindowTextA(hWnd, newText.c_str());
            }
        }
    }

    // Call original function for all other text
    return OriginalSetWindowTextA(hWnd, lpString);
}

// Hook function for SetWindowTextW (Unicode version)
BOOL WINAPI HookedSetWindowTextW(HWND hWnd, LPCWSTR lpString)
{
    if (lpString != nullptr)
    {
        std::wstring text(lpString);

        // Replace "RF Server Now Loading.." with "Nexus Protection"
        if (text.find(L"RF Server Now Loading") != std::wstring::npos)
        {
            return OriginalSetWindowTextW(hWnd, L"Nexus Protection");
        }

        // Also handle variations of the text
        if (text.find(L"RF Server") != std::wstring::npos && text.find(L"Loading") != std::wstring::npos)
        {
            return OriginalSetWindowTextW(hWnd, L"Nexus Protection");
        }

        // Handle window title changes for the main server window
        if (text.find(L"RF Online:") != std::wstring::npos)
        {
            size_t pos = text.find(L"RF Online:");
            if (pos != std::wstring::npos)
            {
                std::wstring newText = text;
                newText.replace(pos, 10, L"Nexus Protection:");
                return OriginalSetWindowTextW(hWnd, newText.c_str());
            }
        }
    }

    return OriginalSetWindowTextW(hWnd, lpString);
}

// Direct function patching approach
typedef __int64 (__fastcall *COpenDlg_OnInitDialog_t)(void* this_ptr);
static COpenDlg_OnInitDialog_t OriginalCOpenDlg_OnInitDialog = nullptr;

// Our replacement function for COpenDlg::OnInitDialog
__int64 __fastcall HookedCOpenDlg_OnInitDialog(void* this_ptr)
{
    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] COpenDlg::OnInitDialog called!" << std::endl;
    #endif

    // Call the original function first
    __int64 result = OriginalCOpenDlg_OnInitDialog(this_ptr);

    // Now set our custom text
    SetWindowTextA((HWND)this_ptr, "Nexus Protection");

    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] Dialog text set to 'Nexus Protection'" << std::endl;
    #endif

    return result;
}

// Function to patch a specific address
BOOL PatchFunction(LPVOID targetAddr, LPVOID newFunction, LPVOID* originalFunction)
{
    DWORD oldProtect;

    // Make the memory writable
    if (!VirtualProtect(targetAddr, 5, PAGE_EXECUTE_READWRITE, &oldProtect))
        return FALSE;

    // Save original bytes (first 5 bytes for JMP instruction)
    *originalFunction = targetAddr;

    // Calculate relative jump offset
    DWORD_PTR offset = (DWORD_PTR)newFunction - (DWORD_PTR)targetAddr - 5;

    // Write JMP instruction (0xE9 + 4-byte offset)
    *(BYTE*)targetAddr = 0xE9;
    *(DWORD*)((BYTE*)targetAddr + 1) = (DWORD)offset;

    // Restore original protection
    VirtualProtect(targetAddr, 5, oldProtect, &oldProtect);

    return TRUE;
}

// Hook function for CreateWindowExA
HWND WINAPI HookedCreateWindowExA(DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName, DWORD dwStyle, int X, int Y, int nWidth, int nHeight, HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam)
{
    if (lpWindowName && (strstr(lpWindowName, "RF Server") || strstr(lpWindowName, "Now Loading")))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Intercepted CreateWindowExA: " << lpWindowName << std::endl;
        #endif
        return OriginalCreateWindowExA(dwExStyle, lpClassName, "Nexus Protection", dwStyle, X, Y, nWidth, nHeight, hWndParent, hMenu, hInstance, lpParam);
    }

    return OriginalCreateWindowExA(dwExStyle, lpClassName, lpWindowName, dwStyle, X, Y, nWidth, nHeight, hWndParent, hMenu, hInstance, lpParam);
}

// Hook function for CreateWindowExW
HWND WINAPI HookedCreateWindowExW(DWORD dwExStyle, LPCWSTR lpClassName, LPCWSTR lpWindowName, DWORD dwStyle, int X, int Y, int nWidth, int nHeight, HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam)
{
    if (lpWindowName && (wcsstr(lpWindowName, L"RF Server") || wcsstr(lpWindowName, L"Now Loading")))
    {
        #ifdef _DEBUG
        std::wcout << L"[Nexus Protection Hook] Intercepted CreateWindowExW: " << lpWindowName << std::endl;
        #endif
        return OriginalCreateWindowExW(dwExStyle, lpClassName, L"Nexus Protection", dwStyle, X, Y, nWidth, nHeight, hWndParent, hMenu, hInstance, lpParam);
    }

    return OriginalCreateWindowExW(dwExStyle, lpClassName, lpWindowName, dwStyle, X, Y, nWidth, nHeight, hWndParent, hMenu, hInstance, lpParam);
}

// Hook function for CreateDialogParamA - this catches dialog creation from resources
HWND WINAPI HookedCreateDialogParamA(HINSTANCE hInstance, LPCSTR lpTemplateName, HWND hWndParent, DLGPROC lpDialogFunc, LPARAM dwInitParam)
{
    // Check if this is dialog resource ID 0x8C (140 decimal) - the COpenDlg dialog
    if ((DWORD_PTR)lpTemplateName == 0x8C)
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Intercepted COpenDlg dialog creation (ID 0x8C)!" << std::endl;
        #endif

        // Create the dialog normally
        HWND hDlg = OriginalCreateDialogParamA(hInstance, lpTemplateName, hWndParent, lpDialogFunc, dwInitParam);

        // Immediately set our custom text
        if (hDlg)
        {
            SetWindowTextA(hDlg, "Nexus Protection");
            #ifdef _DEBUG
            std::cout << "[Nexus Protection Hook] Dialog text changed to 'Nexus Protection'!" << std::endl;
            #endif
        }

        return hDlg;
    }

    return OriginalCreateDialogParamA(hInstance, lpTemplateName, hWndParent, lpDialogFunc, dwInitParam);
}

// Hook function for CreateDialogParamW
HWND WINAPI HookedCreateDialogParamW(HINSTANCE hInstance, LPCWSTR lpTemplateName, HWND hWndParent, DLGPROC lpDialogFunc, LPARAM dwInitParam)
{
    // Check if this is dialog resource ID 0x8C (140 decimal)
    if ((DWORD_PTR)lpTemplateName == 0x8C)
    {
        #ifdef _DEBUG
        std::wcout << L"[Nexus Protection Hook] Intercepted COpenDlg dialog creation (ID 0x8C)!" << std::endl;
        #endif

        HWND hDlg = OriginalCreateDialogParamW(hInstance, lpTemplateName, hWndParent, lpDialogFunc, dwInitParam);

        if (hDlg)
        {
            SetWindowTextW(hDlg, L"Nexus Protection");
            #ifdef _DEBUG
            std::wcout << L"[Nexus Protection Hook] Dialog text changed to 'Nexus Protection'!" << std::endl;
            #endif
        }

        return hDlg;
    }

    return OriginalCreateDialogParamW(hInstance, lpTemplateName, hWndParent, lpDialogFunc, dwInitParam);
}

// Manual API hooking functions
BOOL HookAPI(LPCSTR lpModuleName, LPCSTR lpProcName, LPVOID lpNewFunction, LPVOID* lpOriginalFunction)
{
    HMODULE hModule = GetModuleHandleA(lpModuleName);
    if (!hModule)
        return FALSE;

    LPVOID lpOriginalAddr = GetProcAddress(hModule, lpProcName);
    if (!lpOriginalAddr)
        return FALSE;

    *lpOriginalFunction = lpOriginalAddr;

    DWORD dwOldProtect;
    if (!VirtualProtect(lpOriginalAddr, 5, PAGE_EXECUTE_READWRITE, &dwOldProtect))
        return FALSE;

    // Create a jump to our function (JMP instruction)
    BYTE jmpInstruction[5];
    jmpInstruction[0] = 0xE9; // JMP opcode
    *(DWORD*)(jmpInstruction + 1) = (DWORD)((BYTE*)lpNewFunction - (BYTE*)lpOriginalAddr - 5);

    // Write the jump instruction
    memcpy(lpOriginalAddr, jmpInstruction, 5);

    VirtualProtect(lpOriginalAddr, 5, dwOldProtect, &dwOldProtect);
    return TRUE;
}

// Hook installation function
void InstallHooks()
{
    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] Installing hooks..." << std::endl;
    #endif

    // Method 1: Direct function patching (most reliable)
    HMODULE hModule = GetModuleHandle(NULL); // Get the main executable module
    if (hModule)
    {
        // Calculate the actual address (base + offset)
        LPVOID targetAddr = (LPVOID)((DWORD_PTR)hModule + (COPENDIG_ONINITDIALOG_ADDR - 0x140000000));

        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Module base: 0x" << std::hex << (DWORD_PTR)hModule << std::endl;
        std::cout << "[Nexus Protection Hook] Target address: 0x" << std::hex << (DWORD_PTR)targetAddr << std::endl;
        #endif

        if (PatchFunction(targetAddr, (LPVOID)HookedCOpenDlg_OnInitDialog, (LPVOID*)&OriginalCOpenDlg_OnInitDialog))
        {
            #ifdef _DEBUG
            std::cout << "[Nexus Protection Hook] COpenDlg::OnInitDialog patched successfully!" << std::endl;
            #endif
        }
        else
        {
            #ifdef _DEBUG
            std::cout << "[Nexus Protection Hook] Failed to patch COpenDlg::OnInitDialog!" << std::endl;
            #endif
        }
    }

    // Method 2: API hooks as backup
    if (!HookAPI("user32.dll", "SetWindowTextA", (LPVOID)HookedSetWindowTextA, (LPVOID*)&OriginalSetWindowTextA))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Failed to hook SetWindowTextA!" << std::endl;
        #endif
    }
    else
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] SetWindowTextA hooked successfully!" << std::endl;
        #endif
    }

    if (!HookAPI("user32.dll", "SetWindowTextW", (LPVOID)HookedSetWindowTextW, (LPVOID*)&OriginalSetWindowTextW))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Failed to hook SetWindowTextW!" << std::endl;
        #endif
    }
    else
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] SetWindowTextW hooked successfully!" << std::endl;
        #endif
    }

    if (!HookAPI("user32.dll", "CreateWindowExA", (LPVOID)HookedCreateWindowExA, (LPVOID*)&OriginalCreateWindowExA))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Failed to hook CreateWindowExA!" << std::endl;
        #endif
    }
    else
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] CreateWindowExA hooked successfully!" << std::endl;
        #endif
    }

    if (!HookAPI("user32.dll", "CreateWindowExW", (LPVOID)HookedCreateWindowExW, (LPVOID*)&OriginalCreateWindowExW))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Failed to hook CreateWindowExW!" << std::endl;
        #endif
    }
    else
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] CreateWindowExW hooked successfully!" << std::endl;
        #endif
    }
}

// Hook removal function (simplified for this implementation)
void RemoveHooks()
{
    // Note: This simple implementation doesn't restore original bytes
    // For production use, you'd want to save and restore original bytes
    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] Hooks removed (simplified)!" << std::endl;
    #endif
}

// Function to find and modify existing dialogs
void ModifyExistingDialogs()
{
    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] Scanning for existing dialogs..." << std::endl;
    #endif

    // Find all top-level windows
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        char className[256];
        char windowText[256];
        wchar_t windowTextW[256];

        GetClassNameA(hwnd, className, sizeof(className));
        GetWindowTextA(hwnd, windowText, sizeof(windowText));
        GetWindowTextW(hwnd, windowTextW, sizeof(windowTextW)/sizeof(wchar_t));

        #ifdef _DEBUG
        if (strlen(windowText) > 0)
        {
            std::cout << "[Nexus Protection Hook] Found window: '" << windowText << "' (Class: " << className << ")" << std::endl;
        }
        #endif

        // Check if this is ANY window with RF Server text (not just dialogs)
        bool hasRFText = (strstr(windowText, "RF Server") != nullptr) ||
                        (strstr(windowText, "Now Loading") != nullptr) ||
                        (wcsstr(windowTextW, L"RF Server") != nullptr) ||
                        (wcsstr(windowTextW, L"Now Loading") != nullptr);

        if (hasRFText)
        {
            // Found RF Server window - modify it
            SetWindowTextA(hwnd, "Nexus Protection");
            SetWindowTextW(hwnd, L"Nexus Protection");

            #ifdef _DEBUG
            std::cout << "[Nexus Protection Hook] *** MODIFIED WINDOW: " << windowText << " ***" << std::endl;
            #endif

            // Also check child windows (static text controls, buttons, etc.)
            EnumChildWindows(hwnd, [](HWND hChild, LPARAM lParam) -> BOOL {
                char childText[256];
                wchar_t childTextW[256];
                GetWindowTextA(hChild, childText, sizeof(childText));
                GetWindowTextW(hChild, childTextW, sizeof(childTextW)/sizeof(wchar_t));

                bool hasChildRFText = (strstr(childText, "Now Loading") != nullptr) ||
                                     (strstr(childText, "RF Server") != nullptr) ||
                                     (wcsstr(childTextW, L"Now Loading") != nullptr) ||
                                     (wcsstr(childTextW, L"RF Server") != nullptr);

                if (hasChildRFText)
                {
                    SetWindowTextA(hChild, "Nexus Protection");
                    SetWindowTextW(hChild, L"Nexus Protection");
                    #ifdef _DEBUG
                    std::cout << "[Nexus Protection Hook] *** MODIFIED CHILD: " << childText << " ***" << std::endl;
                    #endif
                }
                return TRUE;
            }, 0);
        }
        return TRUE;
    }, 0);
}

// Timer callback for periodic dialog checking
VOID CALLBACK TimerCallback(HWND hwnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    ModifyExistingDialogs();
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // Install hooks when DLL is loaded
        DisableThreadLibraryCalls(hModule);
        InstallHooks();

        // Optional: Create a console for debugging
        #ifdef _DEBUG
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        std::cout << "[Nexus Protection Hook] DLL loaded and hooks installed!" << std::endl;
        #endif

        // Immediately check for existing dialogs
        ModifyExistingDialogs();

        // Set up a timer to periodically check for new dialogs (every 500ms)
        SetTimer(NULL, 1, 500, TimerCallback);
        break;
        
    case DLL_PROCESS_DETACH:
        // Remove hooks when DLL is unloaded
        RemoveHooks();
        
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] DLL unloaded and hooks removed!" << std::endl;
        FreeConsole();
        #endif
        break;
    }
    return TRUE;
}

// Export function for manual injection (optional)
extern "C" __declspec(dllexport) void InitializeNexusHook()
{
    InstallHooks();
}

extern "C" __declspec(dllexport) void CleanupNexusHook()
{
    RemoveHooks();
}
