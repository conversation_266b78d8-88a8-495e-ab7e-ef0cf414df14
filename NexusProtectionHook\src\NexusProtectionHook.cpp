// NexusProtectionHook.cpp - DLL to hook ZoneServer loading dialog
// Changes "RF Server Now Loading.." to "Nexus Protection"
// Alternative version without Microsoft Detours dependency

#include <windows.h>
#include <string>
#include <iostream>

// Original function pointer
static BOOL (WINAPI* OriginalSetWindowTextA)(HWND hWnd, LPCSTR lpString) = nullptr;

// Our hook function
BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString)
{
    // Check if this is the loading dialog text we want to replace
    if (lpString != nullptr)
    {
        std::string text(lpString);

        // Replace "RF Server Now Loading.." with "Nexus Protection"
        if (text.find("RF Server Now Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }

        // Also handle variations of the text
        if (text.find("RF Server") != std::string::npos && text.find("Loading") != std::string::npos)
        {
            return OriginalSetWindowTextA(hWnd, "Nexus Protection");
        }

        // Handle window title changes for the main server window
        if (text.find("RF Online:") != std::string::npos)
        {
            // Replace "RF Online: ServerName Server" with "Nexus Protection: ServerName Server"
            size_t pos = text.find("RF Online:");
            if (pos != std::string::npos)
            {
                std::string newText = text;
                newText.replace(pos, 10, "Nexus Protection:");
                return OriginalSetWindowTextA(hWnd, newText.c_str());
            }
        }
    }

    // Call original function for all other text
    return OriginalSetWindowTextA(hWnd, lpString);
}

// Manual API hooking functions
BOOL HookAPI(LPCSTR lpModuleName, LPCSTR lpProcName, LPVOID lpNewFunction, LPVOID* lpOriginalFunction)
{
    HMODULE hModule = GetModuleHandleA(lpModuleName);
    if (!hModule)
        return FALSE;

    LPVOID lpOriginalAddr = GetProcAddress(hModule, lpProcName);
    if (!lpOriginalAddr)
        return FALSE;

    *lpOriginalFunction = lpOriginalAddr;

    DWORD dwOldProtect;
    if (!VirtualProtect(lpOriginalAddr, 5, PAGE_EXECUTE_READWRITE, &dwOldProtect))
        return FALSE;

    // Create a jump to our function (JMP instruction)
    BYTE jmpInstruction[5];
    jmpInstruction[0] = 0xE9; // JMP opcode
    *(DWORD*)(jmpInstruction + 1) = (DWORD)((BYTE*)lpNewFunction - (BYTE*)lpOriginalAddr - 5);

    // Write the jump instruction
    memcpy(lpOriginalAddr, jmpInstruction, 5);

    VirtualProtect(lpOriginalAddr, 5, dwOldProtect, &dwOldProtect);
    return TRUE;
}

// Hook installation function
void InstallHooks()
{
    // Hook SetWindowTextA function
    if (!HookAPI("user32.dll", "SetWindowTextA", (LPVOID)HookedSetWindowTextA, (LPVOID*)&OriginalSetWindowTextA))
    {
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] Failed to hook SetWindowTextA!" << std::endl;
        #endif
        return;
    }

    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] SetWindowTextA hooked successfully!" << std::endl;
    #endif
}

// Hook removal function (simplified for this implementation)
void RemoveHooks()
{
    // Note: This simple implementation doesn't restore original bytes
    // For production use, you'd want to save and restore original bytes
    #ifdef _DEBUG
    std::cout << "[Nexus Protection Hook] Hooks removed (simplified)!" << std::endl;
    #endif
}

// Function to find and modify existing dialogs
void ModifyExistingDialogs()
{
    // Find all top-level windows
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        char className[256];
        char windowText[256];

        GetClassNameA(hwnd, className, sizeof(className));
        GetWindowTextA(hwnd, windowText, sizeof(windowText));

        // Check if this is a dialog with RF Server text
        if (strstr(className, "Dialog") || strstr(className, "#32770"))
        {
            if (strstr(windowText, "RF Server") || strstr(windowText, "Now Loading"))
            {
                // Found RF Server dialog - modify it
                SetWindowTextA(hwnd, "Nexus Protection");

                #ifdef _DEBUG
                std::cout << "[Nexus Protection Hook] Modified existing dialog: " << windowText << std::endl;
                #endif

                // Also check child windows (static text controls)
                EnumChildWindows(hwnd, [](HWND hChild, LPARAM lParam) -> BOOL {
                    char childText[256];
                    GetWindowTextA(hChild, childText, sizeof(childText));

                    if (strstr(childText, "Now Loading") || strstr(childText, "RF Server"))
                    {
                        SetWindowTextA(hChild, "Nexus Protection");
                        #ifdef _DEBUG
                        std::cout << "[Nexus Protection Hook] Modified child control: " << childText << std::endl;
                        #endif
                    }
                    return TRUE;
                }, 0);
            }
        }
        return TRUE;
    }, 0);
}

// Timer callback for periodic dialog checking
VOID CALLBACK TimerCallback(HWND hwnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
{
    ModifyExistingDialogs();
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // Install hooks when DLL is loaded
        DisableThreadLibraryCalls(hModule);
        InstallHooks();
        
        // Optional: Create a console for debugging
        #ifdef _DEBUG
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        std::cout << "[Nexus Protection Hook] DLL loaded and hooks installed!" << std::endl;
        #endif
        break;
        
    case DLL_PROCESS_DETACH:
        // Remove hooks when DLL is unloaded
        RemoveHooks();
        
        #ifdef _DEBUG
        std::cout << "[Nexus Protection Hook] DLL unloaded and hooks removed!" << std::endl;
        FreeConsole();
        #endif
        break;
    }
    return TRUE;
}

// Export function for manual injection (optional)
extern "C" __declspec(dllexport) void InitializeNexusHook()
{
    InstallHooks();
}

extern "C" __declspec(dllexport) void CleanupNexusHook()
{
    RemoveHooks();
}
