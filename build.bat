@echo off
echo === Nexus Protection Hook Builder ===
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio compiler not found in PATH.
    echo Please run this from a Visual Studio Developer Command Prompt.
    echo.
    echo Or run: "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

echo Building Nexus Protection Hook DLL...
echo.

REM Build the Hook DLL
cl /LD /EHsc /O2 ^
   /I"C:\Detours\include" ^
   ..\NexusProtectionHook.cpp ^
   /link "C:\Detours\lib.X64\detours.lib" ^
   /OUT:NexusProtectionHook.dll

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build Hook DLL!
    echo Make sure Microsoft Detours is installed in C:\Detours\
    echo Download from: https://github.com/microsoft/Detours
    pause
    exit /b 1
)

echo.
echo Building DLL Injector...
echo.

REM Build the Injector
cl /EHsc /O2 ..\DLLInjector.cpp /OUT:DLLInjector.exe

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERROR: Failed to build DLL Injector!
    pause
    exit /b 1
)

echo.
echo === BUILD SUCCESSFUL! ===
echo.
echo Files created:
echo - NexusProtectionHook.dll (Hook DLL)
echo - DLLInjector.exe (Injection utility)
echo.
echo Usage:
echo 1. Start ZoneServerUD_x64.exe
echo 2. Run DLLInjector.exe
echo 3. The loading dialog will show "Nexus Protection"
echo.

cd ..
pause
