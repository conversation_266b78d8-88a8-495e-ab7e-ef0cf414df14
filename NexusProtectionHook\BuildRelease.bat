@echo off
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if not exist bin mkdir bin
if not exist "bin\Debug" mkdir "bin\Debug"
echo Building RELEASE version (no debug output)...
cl /LD /EHsc /O2 /std:c++17 /MT src\NexusProtectionHook.cpp /Fe:bin\Debug\NexusProtectionHook.dll /Fo:bin\Debug\ kernel32.lib user32.lib
echo.
if exist "bin\Debug\NexusProtectionHook.dll" (
    echo SUCCESS: DLL Built!
    echo Testing injection...
    bin\Debug\DLLInjector.exe
) else (
    echo FAILED: DLL not created!
)
pause
