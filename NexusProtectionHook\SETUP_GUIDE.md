# Visual Studio 2022 Setup Guide

Complete step-by-step guide to compile the Nexus Protection Hook DLL using Visual Studio 2022.

## 🎯 Prerequisites

### 1. Install Visual Studio 2022
- **Download:** https://visualstudio.microsoft.com/downloads/
- **Edition:** Community (free) or Professional/Enterprise
- **Workloads Required:**
  - ✅ **Desktop development with C++**
  - ✅ **MSVC v143 - VS 2022 C++ x64/x86 build tools**
  - ✅ **Windows 10/11 SDK**

### 2. Install Microsoft Detours
- **Download:** https://github.com/microsoft/Detours
- **Extract to:** `C:\Detours\`
- **Build Detours:**
  ```batch
  cd C:\Detours
  nmake
  ```

## 🚀 Step-by-Step Compilation

### Step 1: Open the Project

1. **Launch Visual Studio 2022**
2. **Click:** "Open a project or solution"
3. **Navigate to:** `NexusProtectionHook` folder
4. **Select:** `NexusProtectionHook.sln`
5. **Click:** "Open"

### Step 2: Configure the Solution

1. **Set Platform to x64:**
   - Top toolbar: Change from "x86" to **"x64"**
   
2. **Choose Configuration:**
   - **Debug:** For testing (includes console output)
   - **Release:** For production (optimized, smaller file)

### Step 3: Verify Detours Path

1. **Right-click** on "NexusProtectionHook" project
2. **Select:** "Properties"
3. **Go to:** Configuration Properties → C/C++ → General
4. **Check:** Additional Include Directories shows `C:\Detours\include`
5. **Go to:** Configuration Properties → Linker → General
6. **Check:** Additional Library Directories shows `C:\Detours\lib.X64`

### Step 4: Build the Solution

1. **Build Menu** → **Build Solution** (or press `Ctrl+Shift+B`)
2. **Wait for compilation** to complete
3. **Check Output window** for any errors

### Step 5: Locate Output Files

After successful build, files will be in:
```
NexusProtectionHook/
├── bin/
│   ├── Debug/          # Debug builds
│   │   ├── NexusProtectionHook.dll
│   │   └── DLLInjector.exe
│   └── Release/        # Release builds
│       ├── NexusProtectionHook.dll
│       └── DLLInjector.exe
```

## 🔧 Using the Compiled DLL

### Method 1: Using the Injector (Recommended)

1. **Start ZoneServerUD_x64.exe**
2. **Run the injector:**
   ```batch
   cd NexusProtectionHook\bin\Release
   DLLInjector.exe
   ```
3. **Verify:** Loading dialog shows "Nexus Protection"

### Method 2: Manual Injection

1. **Use Process Hacker:**
   - Download: https://processhacker.sourceforge.io/
   - Right-click ZoneServerUD_x64.exe process
   - Select "Miscellaneous" → "Inject DLL"
   - Browse to `NexusProtectionHook.dll`

2. **Use Cheat Engine:**
   - Open Cheat Engine
   - Select ZoneServerUD_x64.exe process
   - Memory View → Tools → Inject DLL
   - Select `NexusProtectionHook.dll`

## 🐛 Troubleshooting

### Build Errors

**Error: "Cannot open include file: 'detours.h'"**
```
Solution:
1. Install Microsoft Detours to C:\Detours\
2. Build Detours using nmake
3. Verify paths in project properties
```

**Error: "Cannot open file 'detours.lib'"**
```
Solution:
1. Make sure Detours is built (run nmake in C:\Detours\)
2. Check that C:\Detours\lib.X64\detours.lib exists
3. Verify library path in project properties
```

**Error: "Platform toolset 'v143' is not installed"**
```
Solution:
1. Install Visual Studio 2022 with C++ workload
2. Or change toolset to v142 (VS 2019) in project properties
```

### Runtime Errors

**Error: "ZoneServerUD_x64.exe not found"**
```
Solution:
1. Start the zone server before running injector
2. Make sure process name matches exactly
3. Check if running as administrator is needed
```

**Error: "Failed to inject DLL"**
```
Solution:
1. Run injector as Administrator
2. Disable antivirus temporarily
3. Check if DLL and EXE are same architecture (x64)
```

## 🎮 Advanced Configuration

### Custom Text Replacement

Edit `src/NexusProtectionHook.cpp` line 20:
```cpp
// Change this line to customize the text
return TrueSetWindowTextA(hWnd, "Your Custom Text Here");
```

### Debug Mode

To enable debug console output:
1. **Build in Debug configuration**
2. **Or add** `#define _DEBUG` at top of NexusProtectionHook.cpp
3. **Rebuild** the project

### Multiple Replacements

Add more text replacements in the hook function:
```cpp
// Add more replacements
if (text.find("Other Text") != std::string::npos)
{
    return TrueSetWindowTextA(hWnd, "Replacement Text");
}
```

## 📁 Project Structure

```
NexusProtectionHook/
├── NexusProtectionHook.sln      # Visual Studio Solution
├── NexusProtectionHook.vcxproj  # Hook DLL Project
├── DLLInjector.vcxproj         # Injector Project
├── src/                        # Source files
├── bin/                        # Output files
└── build/                      # Build intermediate files
```

## 🔒 Security Notes

- **Antivirus:** May flag DLL injection as suspicious
- **UAC:** May require administrator privileges
- **Firewall:** Should not be affected
- **Game Protection:** Some anti-cheat systems may detect injection

## 📝 Tips

1. **Always build in x64** - Zone server is 64-bit
2. **Use Release build** for final version (smaller, faster)
3. **Test with Debug build** first (has console output)
4. **Keep Detours updated** for compatibility
5. **Backup original files** before testing

## 🤝 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Verify all prerequisites are installed
3. Try building a simple "Hello World" DLL first
4. Check Visual Studio output window for detailed errors
