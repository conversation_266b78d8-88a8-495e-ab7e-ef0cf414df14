// DirectPatchHook.cpp - Advanced hook using pattern scanning and direct patching
// Targets the exact COpenDlg::OnInitDialog function from decompiled source

#include <windows.h>
#include <string>
#include <iostream>
#include <vector>

// Function signatures from decompiled source
typedef __int64 (__fastcall *COpenDlg_OnInitDialog_t)(void* this_ptr);
static COpenDlg_OnInitDialog_t OriginalCOpenDlg_OnInitDialog = nullptr;
static BYTE OriginalBytes[12]; // Store original bytes for restoration

// Our replacement function
__int64 __fastcall HookedCOpenDlg_OnInitDialog(void* this_ptr)
{
    // Restore original bytes temporarily to call original function
    DWORD oldProtect;
    VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, PAGE_EXECUTE_READWRITE, &oldProtect);
    memcpy(OriginalCOpenDlg_OnInitDialog, OriginalBytes, 12);
    VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, oldProtect, &oldProtect);
    
    // Call original function
    __int64 result = OriginalCOpenDlg_OnInitDialog(this_ptr);
    
    // Re-install our hook
    VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, PAGE_EXECUTE_READWRITE, &oldProtect);
    
    // Calculate relative jump offset
    DWORD_PTR offset = (DWORD_PTR)HookedCOpenDlg_OnInitDialog - (DWORD_PTR)OriginalCOpenDlg_OnInitDialog - 5;
    
    // Write JMP instruction
    *(BYTE*)OriginalCOpenDlg_OnInitDialog = 0xE9;
    *(DWORD*)((BYTE*)OriginalCOpenDlg_OnInitDialog + 1) = (DWORD)offset;
    
    VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, oldProtect, &oldProtect);
    
    // Now set our custom text - this is the key part!
    SetWindowTextA((HWND)this_ptr, "Nexus Protection");
    
    std::cout << "[Nexus Protection Hook] Dialog text changed to 'Nexus Protection'!" << std::endl;
    
    return result;
}

// Pattern scanning function
LPVOID FindPattern(HMODULE hModule, const char* pattern, const char* mask)
{
    MODULEINFO modInfo;
    GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(MODULEINFO));
    
    DWORD_PTR startAddr = (DWORD_PTR)hModule;
    DWORD_PTR endAddr = startAddr + modInfo.SizeOfImage;
    
    size_t patternLen = strlen(mask);
    
    for (DWORD_PTR addr = startAddr; addr < endAddr - patternLen; addr++)
    {
        bool found = true;
        for (size_t i = 0; i < patternLen; i++)
        {
            if (mask[i] == 'x' && *(BYTE*)(addr + i) != (BYTE)pattern[i])
            {
                found = false;
                break;
            }
        }
        if (found)
            return (LPVOID)addr;
    }
    return nullptr;
}

// Install the direct patch
BOOL InstallDirectPatch()
{
    HMODULE hModule = GetModuleHandle(NULL);
    if (!hModule)
        return FALSE;
    
    // Pattern for COpenDlg::OnInitDialog function start
    // This is based on the decompiled code structure
    const char pattern[] = "\x48\x89\x5C\x24\x08\x57\x48\x83\xEC\x20";
    const char mask[] = "xxxxxxxxxx";
    
    LPVOID targetAddr = FindPattern(hModule, pattern, mask);
    if (!targetAddr)
    {
        std::cout << "[Nexus Protection Hook] Could not find COpenDlg::OnInitDialog pattern!" << std::endl;
        return FALSE;
    }
    
    std::cout << "[Nexus Protection Hook] Found COpenDlg::OnInitDialog at: 0x" << std::hex << (DWORD_PTR)targetAddr << std::endl;
    
    // Save original bytes
    memcpy(OriginalBytes, targetAddr, 12);
    OriginalCOpenDlg_OnInitDialog = (COpenDlg_OnInitDialog_t)targetAddr;
    
    // Install hook
    DWORD oldProtect;
    if (!VirtualProtect(targetAddr, 12, PAGE_EXECUTE_READWRITE, &oldProtect))
        return FALSE;
    
    // Calculate relative jump offset
    DWORD_PTR offset = (DWORD_PTR)HookedCOpenDlg_OnInitDialog - (DWORD_PTR)targetAddr - 5;
    
    // Write JMP instruction
    *(BYTE*)targetAddr = 0xE9;
    *(DWORD*)((BYTE*)targetAddr + 1) = (DWORD)offset;
    
    // Fill remaining bytes with NOPs
    for (int i = 5; i < 12; i++)
        *((BYTE*)targetAddr + i) = 0x90;
    
    VirtualProtect(targetAddr, 12, oldProtect, &oldProtect);
    
    std::cout << "[Nexus Protection Hook] Direct patch installed successfully!" << std::endl;
    return TRUE;
}

// Remove the patch
void RemoveDirectPatch()
{
    if (OriginalCOpenDlg_OnInitDialog)
    {
        DWORD oldProtect;
        VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, PAGE_EXECUTE_READWRITE, &oldProtect);
        memcpy(OriginalCOpenDlg_OnInitDialog, OriginalBytes, 12);
        VirtualProtect(OriginalCOpenDlg_OnInitDialog, 12, oldProtect, &oldProtect);
        
        std::cout << "[Nexus Protection Hook] Direct patch removed!" << std::endl;
    }
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        
        // Create console for debugging
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        std::cout << "[Nexus Protection Hook] Direct Patch DLL loaded!" << std::endl;
        
        // Install the direct patch
        if (InstallDirectPatch())
        {
            std::cout << "[Nexus Protection Hook] Ready to intercept dialog creation!" << std::endl;
        }
        else
        {
            std::cout << "[Nexus Protection Hook] Failed to install direct patch!" << std::endl;
        }
        break;
        
    case DLL_PROCESS_DETACH:
        RemoveDirectPatch();
        std::cout << "[Nexus Protection Hook] Direct Patch DLL unloaded!" << std::endl;
        FreeConsole();
        break;
    }
    return TRUE;
}

// Export functions
extern "C" __declspec(dllexport) void InitializeDirectPatch()
{
    InstallDirectPatch();
}

extern "C" __declspec(dllexport) void CleanupDirectPatch()
{
    RemoveDirectPatch();
}
