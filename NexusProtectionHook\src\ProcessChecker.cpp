// ProcessChecker.cpp - Tool to see what processes are actually running

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>

class ProcessChecker
{
public:
    static void ListAllProcesses()
    {
        std::cout << "=== ALL RUNNING PROCESSES ===" << std::endl;
        std::cout << std::endl;
        
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
        {
            std::cout << "Failed to create process snapshot!" << std::endl;
            return;
        }
        
        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);
        
        if (!Process32FirstW(hSnapshot, &pe32))
        {
            std::cout << "Failed to get first process!" << std::endl;
            CloseHandle(hSnapshot);
            return;
        }
        
        std::vector<std::wstring> allProcesses;
        
        do
        {
            allProcesses.push_back(pe32.szExeFile);
        } while (Process32NextW(hSnapshot, &pe32));
        
        CloseHandle(hSnapshot);
        
        // Sort and display
        std::sort(allProcesses.begin(), allProcesses.end());
        
        int count = 0;
        for (const auto& processName : allProcesses)
        {
            std::wcout << L"  " << processName << std::endl;
            count++;
            
            // Look for anything that might be zone server related
            std::wstring lowerName = processName;
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::towlower);
            
            if (lowerName.find(L"zone") != std::wstring::npos ||
                lowerName.find(L"server") != std::wstring::npos ||
                lowerName.find(L"rf") != std::wstring::npos)
            {
                std::wcout << L"    ^^^ POTENTIAL MATCH: " << processName << std::endl;
            }
        }
        
        std::cout << std::endl;
        std::cout << "Total processes: " << count << std::endl;
    }
    
    static DWORD FindProcess(const std::wstring& processName)
    {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
            return 0;
        
        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);
        
        if (Process32FirstW(hSnapshot, &pe32))
        {
            do
            {
                if (processName == pe32.szExeFile)
                {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }
        
        CloseHandle(hSnapshot);
        return 0;
    }
};

int main(int argc, char* argv[])
{
    std::cout << "=== Process Checker & Zone Server Finder ===" << std::endl;
    std::cout << std::endl;
    
    // First, let's see if we can find any zone server processes
    std::vector<std::wstring> targetProcesses = {
        L"ZoneServerUD_x64.exe",
        L"ZoneServer.exe",
        L"ZoneServerUD.exe",
        L"zoneserver.exe",
        L"ZoneServerUD_x64",
        L"ZoneServer",
        L"ZoneServerUD"
    };
    
    std::cout << "Looking for Zone Server processes..." << std::endl;
    bool found = false;
    
    for (const auto& processName : targetProcesses)
    {
        DWORD pid = ProcessChecker::FindProcess(processName);
        if (pid != 0)
        {
            std::wcout << L"FOUND: " << processName << L" (PID: " << pid << L")" << std::endl;
            found = true;
        }
        else
        {
            std::wcout << L"Not found: " << processName << std::endl;
        }
    }
    
    if (!found)
    {
        std::cout << std::endl;
        std::cout << "No zone server processes found!" << std::endl;
        std::cout << std::endl;
        std::cout << "Let's check ALL running processes to see what's there:" << std::endl;
        std::cout << std::endl;
        
        ProcessChecker::ListAllProcesses();
        
        std::cout << std::endl;
        std::cout << "INSTRUCTIONS:" << std::endl;
        std::cout << "1. Start your zone server (ZoneServerUD_x64.exe)" << std::endl;
        std::cout << "2. Keep it running (don't close it)" << std::endl;
        std::cout << "3. Run this tool again to see if it appears in the list" << std::endl;
        std::cout << "4. Look for any process with 'zone', 'server', or 'rf' in the name" << std::endl;
    }
    else
    {
        std::cout << std::endl;
        std::cout << "Great! Zone server process is running." << std::endl;
        std::cout << "The injector should be able to find it now." << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    return 0;
}
