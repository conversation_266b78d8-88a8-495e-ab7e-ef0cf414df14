// Decompiled Function: _dynamic_atexit_destructor_for__g_Player__
// Address: 0x1406E8940

void __fastcall dynamic_atexit_destructor_for__g_Player__()
{
  __int64 *v0; // rdi
  __int64 i; // rcx
  __int64 v2; // [rsp+0h] [rbp-28h] BYREF

  v0 = &v2;
  for ( i = 8LL; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector destructor iterator'((char *)g_Player, 0xC6A8uLL, 2532, (void (__fastcall *)(void *))CPlayer::~CPlayer);
}

