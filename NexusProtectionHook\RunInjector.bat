@echo off
title Nexus Protection Hook Injector
echo.
echo ===================================
echo   Nexus Protection Hook Injector
echo ===================================
echo.

REM Check if we're running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator: YES
) else (
    echo Running as Administrator: NO
    echo.
    echo WARNING: You may need administrator privileges for injection.
    echo If injection fails, try running as administrator.
)

echo.
echo Looking for DLL Injector...

REM Try to find the injector in different locations
if exist "DLLInjector.exe" (
    echo Found: DLLInjector.exe
    DLLInjector.exe
) else if exist "bin\DLLInjector.exe" (
    echo Found: bin\DLLInjector.exe
    bin\DLLInjector.exe
) else if exist "bin\Release\DLLInjector.exe" (
    echo Found: bin\Release\DLLInjector.exe
    bin\Release\DLLInjector.exe
) else if exist "bin\Debug\DLLInjector.exe" (
    echo Found: bin\Debug\DLLInjector.exe
    bin\Debug\DLLInjector.exe
) else (
    echo.
    echo ERROR: DLLInjector.exe not found!
    echo.
    echo Please build the project first:
    echo 1. Open NexusProtectionHook.sln in Visual Studio 2022
    echo 2. Set platform to x64
    echo 3. Build Solution (Ctrl+Shift+B)
    echo.
    pause
    exit /b 1
)

echo.
pause
