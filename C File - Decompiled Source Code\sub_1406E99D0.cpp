// Decompiled Function: sub_1406E99D0
// Address: 0x1406E99D0

void __fastcall sub_1406E99D0()
{
  CryptoPP::simple_ptr<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>>>::~simple_ptr<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>>>((void **)&qword_184A8A338);
}

