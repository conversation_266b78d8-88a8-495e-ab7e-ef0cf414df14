# Nexus Protection Hook DLL

A DLL injection solution to change the RF Online Zone Server loading dialog from "RF Server Now Loading.." to "Nexus Protection".

## 🎯 Features

- **No Source Code Modification Required** - Works with existing compiled ZoneServerUD_x64.exe
- **Real-time Text Replacement** - Hooks SetWindowTextA to replace text dynamically
- **Multiple Text Replacements** - Handles both loading dialog and main window titles
- **Easy to Use** - Simple injection process with included injector tool
- **Reversible** - Can be unloaded without affecting the server

## 📋 Requirements

- **Windows 10/11** (64-bit)
- **Microsoft Visual Studio 2019/2022** (for building)
- **Microsoft Detours Library** (for API hooking)
- **ZoneServerUD_x64.exe** (RF Online Zone Server)

## 🛠️ Installation

### Step 1: Install Microsoft Detours

1. Download Detours from: https://github.com/microsoft/Detours
2. Extract to `C:\Detours\` (or update paths in build.bat)
3. Build Detours following their instructions

### Step 2: Build the Hook DLL

**Option A: Using Visual Studio Developer Command Prompt**
```batch
# Open Visual Studio Developer Command Prompt (x64)
cd /path/to/nexus-protection-hook
build.bat
```

**Option B: Using CMake**
```batch
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### Step 3: Files Created

After building, you'll have:
- `NexusProtectionHook.dll` - The hook DLL
- `DLLInjector.exe` - Injection utility

## 🚀 Usage

### Method 1: Automatic Injection (Recommended)

1. **Start the Zone Server**
   ```batch
   cd "2_ZoneServer\RF_Bin"
   ZoneServerUD_x64.exe
   ```

2. **Run the Injector**
   ```batch
   DLLInjector.exe
   ```

3. **Verify the Change**
   - The loading dialog should now show "Nexus Protection"
   - Main window title will show "Nexus Protection: ServerName Server"

### Method 2: Manual DLL Loading (Advanced)

You can also inject the DLL using other tools like:
- **Process Hacker** (Inject DLL feature)
- **Cheat Engine** (Memory View → Tools → Inject DLL)
- **Custom injection tools**

## 🔧 How It Works

### Hook Mechanism
```cpp
// The DLL hooks SetWindowTextA function
BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString)
{
    if (lpString && strstr(lpString, "RF Server Now Loading"))
    {
        return TrueSetWindowTextA(hWnd, "Nexus Protection");
    }
    return TrueSetWindowTextA(hWnd, lpString);
}
```

### Text Replacements
- `"RF Server Now Loading.."` → `"Nexus Protection"`
- `"RF Online: ServerName Server"` → `"Nexus Protection: ServerName Server"`

## 🐛 Troubleshooting

### Build Issues

**Error: "detours.h not found"**
- Install Microsoft Detours library
- Update paths in `build.bat` or `CMakeLists.txt`

**Error: "cl is not recognized"**
- Run from Visual Studio Developer Command Prompt
- Or run: `"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"`

### Runtime Issues

**Error: "ZoneServerUD_x64.exe not found"**
- Make sure the zone server is running before injection
- Check the process name matches exactly

**Error: "Failed to inject DLL"**
- Run injector as Administrator
- Check antivirus isn't blocking the injection
- Ensure DLL and EXE are same architecture (x64)

## 🔒 Security Notes

- **Antivirus Detection** - Some antivirus may flag DLL injection as suspicious
- **Administrator Rights** - May require admin privileges for injection
- **Process Protection** - Some security software may prevent injection

## 📁 File Structure

```
nexus-protection-hook/
├── NexusProtectionHook.cpp    # Main hook DLL source
├── NexusProtectionHook.h      # Header file
├── DLLInjector.cpp           # Injection utility source
├── CMakeLists.txt            # CMake build file
├── build.bat                 # Windows build script
└── README.md                 # This file
```

## 🎮 Advanced Usage

### Custom Text Replacement

Edit `NexusProtectionHook.cpp` to change the replacement text:

```cpp
// Change this line to customize the text
return TrueSetWindowTextA(hWnd, "Your Custom Text Here");
```

### Multiple Servers

The hook works with multiple zone server instances simultaneously.

### Debugging

Build with `_DEBUG` flag to enable console output:
```cpp
#define _DEBUG  // Add this line for debug output
```

## 📝 License

This project is for educational and personal use only. Respect the original RF Online terms of service.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all requirements are met
3. Test with a clean zone server installation
